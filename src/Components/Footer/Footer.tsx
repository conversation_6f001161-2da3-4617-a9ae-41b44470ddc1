import React, { useState } from "react";
import gbi_logo from "../../assests/images/gti_logo.svg";
import {
  BsFacebook,
  BsTwitter,
  BsLinkedin,
  BsEnvelope,
  BsArrowRight,
} from "react-icons/bs";
import { Link, NavLink } from "react-router-dom";
import * as routes from "../../Components/constants";
import "./styles.css";

const Footer = () => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Handle newsletter subscription
      console.log("Newsletter subscription:", email);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setEmail("");
      // Could add success notification here
    } catch (error) {
      console.error("Newsletter subscription failed:", error);
      // Could add error notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <footer className="modern-footer footer-animate">
      <div className="footer-particles"></div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400/30 rounded-full animate-ping"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-purple-400/40 rounded-full animate-pulse"></div>
        <div className="absolute bottom-32 left-1/4 w-3 h-3 bg-cyan-400/20 rounded-full animate-bounce"></div>
        <div
          className="absolute bottom-20 right-1/3 w-1.5 h-1.5 bg-indigo-400/30 rounded-full animate-ping"
          style={{ animationDelay: "1s" }}
        ></div>
      </div>

      <div className="footer-content">
        {/* Top Section */}
        <div className="footer-top">
          {/* Brand Section */}
          <div className="footer-brand">
            <div className="footer-logo-container">
              <Link to="/" className="flex items-center">
                <img src={gbi_logo} className="footer-logo" alt="GTI® Logo" />
              </Link>
            </div>

            <h3 className="footer-tagline">Global Tech Interface</h3>

            <p className="footer-brand-text">
              Connecting innovators worldwide through cutting-edge technology
              solutions. Empowering businesses to transform ideas into reality
              with our comprehensive platform for technology discovery and
              collaboration.
            </p>

            <div className="footer-social">
              <a
                href="https://twitter.com/GTI_Techglobal"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon"
                aria-label="Follow us on Twitter"
              >
                <BsTwitter />
              </a>
              <a
                href="https://www.facebook.com/people/Global-Tech-Interface/100063974445081/"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon"
                aria-label="Follow us on Facebook"
              >
                <BsFacebook />
              </a>
              <a
                href="https://www.linkedin.com/company/globaltech-interface/about/"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon"
                aria-label="Follow us on LinkedIn"
              >
                <BsLinkedin />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="social-icon"
                aria-label="Contact us via email"
              >
                <BsEnvelope />
              </a>
            </div>
          </div>
          {/* Platform Section */}
          <div className="footer-nav-section">
            <h3 className="footer-nav-title">Platform</h3>
            <ul className="footer-nav-list">
              <li>
                <Link to={routes.ABOUT} className="footer-nav-item">
                  About GTI®
                </Link>
              </li>
              <li>
                <Link to={routes.TECHNOLOGY} className="footer-nav-item">
                  Technologies
                </Link>
              </li>
              <li>
                <Link to={routes.OPPORTUNITY} className="footer-nav-item">
                  Opportunities
                </Link>
              </li>
              <li>
                <Link to={routes.INNOVATION} className="footer-nav-item">
                  Innovation Calls
                </Link>
              </li>
              {/* <li>
                <Link to={routes.PRICING} className="footer-nav-item">
                  Pricing
                </Link>
              </li> */}
            </ul>
          </div>

          {/* Resources Section */}
          <div className="footer-nav-section">
            <h3 className="footer-nav-title">Resources</h3>
            <ul className="footer-nav-list">
              <li>
                <Link to={routes.ARTICLES} className="footer-nav-item">
                  Articles
                </Link>
              </li>
              <li>
                <Link to={routes.EVENTS} className="footer-nav-item">
                  Events
                </Link>
              </li>
              <li>
                <Link to={routes.PUBLICATIONS} className="footer-nav-item">
                  Publications
                </Link>
              </li>
              <li>
                <Link to={routes.NEWS} className="footer-nav-item">
                  News
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter Section */}
          <div className="footer-newsletter">
            <h3 className="newsletter-title">Stay Updated</h3>
            <p className="newsletter-description">
              Get the latest updates on technology trends, innovation
              opportunities, and platform features delivered to your inbox.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="newsletter-form">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="newsletter-input"
                required
                disabled={isSubmitting}
                autoComplete="email"
                aria-label="Email address for newsletter subscription"
              />
              <button
                type="submit"
                className={`newsletter-button ${
                  isSubmitting ? "opacity-75 cursor-not-allowed" : ""
                }`}
                disabled={isSubmitting}
                aria-label={
                  isSubmitting ? "Subscribing..." : "Subscribe to newsletter"
                }
              >
                {isSubmitting ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <BsArrowRight className="w-5 h-5" />
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="footer-copyright">
              <p>© {currentYear} Global Tech Interface. All rights reserved.</p>
              <p className="mt-2">
                Powered by{" "}
                <a
                  href="https://www.globalbusinessinroads.com"
                  className="hover:text-blue-400 transition-colors duration-300"
                  target="_blank"
                  rel="noreferrer"
                >
                  Global Business Inroads
                </a>
              </p>
            </div>

            <div className="footer-legal-links">
              <NavLink to={routes.TERMS} className="footer-legal-link">
                Terms & Conditions
              </NavLink>
              <span className="footer-legal-separator">•</span>
              <NavLink to={routes.PRIVACY} className="footer-legal-link">
                Privacy Policy
              </NavLink>
              <span className="footer-legal-separator">•</span>
              <NavLink
                to={routes.REFUND_AND_CANCELLATION_POLICY}
                className="footer-legal-link"
              >
                Refund Policy
              </NavLink>
              <span className="footer-legal-separator">•</span>
              <Link to={routes.CONTACT_REQUEST} className="footer-legal-link">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
